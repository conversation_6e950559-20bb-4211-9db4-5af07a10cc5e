# Name your project! Project names should contain only lowercase characters
# and underscores. A good package name should reflect your organization's
# name or the intended use of these models
name: 'fanfix_dbt'
version: '1.0.0'
dbt-cloud:
    project-id: 70471823488599

# This setting configures which "profile" dbt uses for this project.
profile: 'dbt_cloud'

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"

vars:
  $TIMEZONE: 'UTC'
  $START_DATE: '2023-01-01'
  $END_DATE: '2026-12-01'
  env: 'dev'
  fanfix_database: fanfix_raw

# Configuring models
# Full documentation: https://docs.getdbt.com/docs/configuring-models

# In dbt, the default materialization for a model is a view. This means, when you run 
# dbt run or dbt build, all of your models will be built as a view in your data platform. 
# The configuration below will override this setting for models in the example folder to 
# instead be materialized as tables. Any models you add to the root of the models folder will 
# continue to be built as views. These settings can be overridden in the individual model files
# using the `{{ config(...) }}` macro.

models:
  dbt:
    # Applies to all files under models/example/
    example:
      +materialized: table

    revenue:
      +materialized: table
      +schema: analytics
      +tags: ["revenue"]

    creators:
      +materialized: table
      +schema: analytics
      +tags: ["creator"]
    
    fans:
      +materialized: table
      +schema: analytics
      +tags: ["fan", "user"]

    retention:
      +materialized: table
      +schema: analytics
      +tags: ["fan", "revenue"]