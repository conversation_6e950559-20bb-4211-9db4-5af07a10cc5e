{{ config(materialized='table') }}

WITH creator_revenue_by_day AS (
    SELECT DISTINCT
        charges.payment_intent
        , charges.id AS charge_id
        , charges.captured_at AS time
        , charges.card_country
        , charges.card_funding
        , tax.card_type AS payment_method
        , charges.card_id
        , {{ charge_type('charges.description') }} AS type
        -- , REGEXP_SUBSTR(
        --             LOWER(charges.description),
        --             '(subscription creation|subscription update|subscription charge|creator tip|media unlock|message|post unlock|post|publication|livestream unlock|livestream|invoice)',
        --             1
        --     ) AS type
        , am.account_id AS account_id
        , am.value AS creator_fanfix_id
        , cm.value AS fan_fanfix_id
        , CAST((charges.amount / 100) AS DECIMAL(10, 2)) AS gross_revenue
        , COALESCE(tax.tax_amount, 0) AS sales_tax
        , CAST((charges.amount / 100) - COALESCE(tax.tax_amount, 0) - COALESCE(tax.processing_fee, 0) AS DECIMAL(10, 2)) AS sub_total
        , CAST((charges.amount / 100) - COALESCE(application_fees.amount / 100, 0) AS DECIMAL(10, 2)) AS net_revenue -- net revenue for agency
        , CAST(COALESCE(application_fees.amount / 100, 0) AS DECIMAL(10, 3)) AS total_fee
        , CASE
            WHEN tax.application_fee IS NOT NULL AND tax.application_fee > 0 THEN tax.application_fee
            ELSE GREATEST(CAST((application_fees.amount / 100) - COALESCE(tax.tax_amount, 0) AS DECIMAL(10, 2)), 0)
        END AS platform_fee -- IN prevent of missing platform fee FROM PAYMENT db. (fanfix fee)
        , COALESCE(tax.processing_fee, 0)  AS processing_fee  --(stripe fee)
    FROM FANFIX_STRIPE.STRIPE.CHARGES charges 
    LEFT JOIN (SELECT * FROM FANFIX_STRIPE.STRIPE.CONNECTED_ACCOUNTS_METADATA WHERE key = 'id') am ON am.account_id = charges.destination_id
    LEFT JOIN (SELECT * FROM FANFIX_STRIPE.STRIPE.CUSTOMERS_METADATA WHERE key = 'appUserId') cm ON charges.customer_id = cm.customer_id
    LEFT JOIN FANFIX_STRIPE.STRIPE.APPLICATION_FEES ON application_fees.id = charges.application_fee_id
    LEFT JOIN {{ ref('fact_payment_tax_collection') }} tax ON charges.payment_intent = tax.payment_processor_transaction_id
    WHERE 1 = 1
        AND paid = true
        AND charges.amount > 0
        AND charges.status = 'succeeded'
        AND charges.merchant_id = 'acct_1Ir538D2AfqkY9Hk'

),
name_resolved_revenue AS (
    SELECT
        creator_revenue_by_day.charge_id
        , creator_revenue_by_day.time
        , card_country
        , card_funding
        , payment_method
        , card_id
        , CASE
            WHEN creator_revenue_by_day.type = 'publication' THEN 'tip jar'
            ELSE creator_revenue_by_day.type
        END AS type
        , CASE WHEN type like '%subscription%' THEN 'subscription' ELSE type END AS revenue_category
        , account_id
        , creator_fanfix_id
        , creator.username AS creator_username
        , fan_fanfix_id
        , fan.username AS fan_username
        , creator_revenue_by_day.sales_tax
        , creator_revenue_by_day.total_fee
        , creator_revenue_by_day.platform_fee
        , creator_revenue_by_day.net_revenue
        , creator_revenue_by_day.gross_revenue
        , creator_revenue_by_day.net_revenue * 1.25 AS external_reporting_gross_revenue
        , creator_revenue_by_day.sub_total
        , creator_revenue_by_day.processing_fee
        , row_number() over (partition by creator_fanfix_id, fan_fanfix_id, type order by time asc) as payment_occurance_rank_by_pair 
    FROM CREATOR_REVENUE_BY_DAY
    LEFT JOIN FANFIX_RAW.FANFIX.USER_PROFILES creator ON creator.user_id = creator_fanfix_id
    LEFT JOIN FANFIX_RAW.FANFIX.USER_PROFILES fan ON fan.user_id = fan_fanfix_id
    ),
dispute_counts AS (
    SELECT 
        id AS dispute_id
        , charge_id
        , created AS dispute_created_time
        , reason
        , status
        , row_number() over (partition by charge_id ORDER BY created desc) AS dispute_rank
    FROM FANFIX_STRIPE.STRIPE.DISPUTES
),
dedupped_disputes AS (
    SELECT 
        *
    FROM DISPUTE_COUNTS
    WHERE dispute_rank = 1
)
SELECT 
    r.*
    , dispute_id
    , dispute_created_time
    , reason, status
    , CASE 
        WHEN revenue_category like '%subscription%' and payment_occurance_rank_by_pair = 1 then 'new subscription'
        WHEN revenue_category like '%subscription%' and payment_occurance_rank_by_pair > 1 then 'renew subscription'
        ELSE revenue_category
    END AS revenue_category_detail
FROM NAME_RESOLVED_REVENUE r
LEFT JOIN DEDUPPED_DISPUTES dd ON dd.charge_id = r.charge_id
WHERE account_id is NOT null
ORDER BY time DESC
