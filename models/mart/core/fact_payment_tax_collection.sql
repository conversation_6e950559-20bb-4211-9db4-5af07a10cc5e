{{ config(materialized='table') }}

SELECT
    p.created_at
    , p.payment_processor
    , p.payment_processor_transaction_id
    , p.tip_type
    , p.creator_id
    , p.fan_id
    -- , JSON_EXTRACT_PATH_TEXT(SALES_TAX_BILLING_LOCATION, 'countryCode') AS sales_tax_countryCode
    -- , JSON_EXTRACT_PATH_TEXT(SALES_TAX_BILLING_LOCATION, 'region') AS sales_tax_region
    -- , JSON_EXTRACT_PATH_TEXT(SALES_TAX_BILLING_LOCATION, 'zip') AS sales_tax_zip
    , (p.amount_in_cents / 100) AS sales_amount
    , (p.sales_tax_amount_in_cents / 100) AS tax_amount
    , (p.APPLICATION_FEE_AMOUNT_IN_CENTS / 100) AS application_fee
    , (p.PROCESSING_FEE_AMOUNT_IN_CENTS / 100) AS processing_fee
    , p.SALES_TAX_BILLING_LOCATION
    , UPPER(REPLACE(try_parse_json(p.SALES_TAX_BILLING_LOCATION):countryCode, '"','')) AS sales_tax_countryCode
    , UPPER(REPLACE(try_parse_json(p.SALES_TAX_BILLING_LOCATION):region, '"','')) AS sales_tax_region
    , REPLACE(try_parse_json(p.SALES_TAX_BILLING_LOCATION):zip, '"','') AS sales_tax_zip
    , CASE
        WHEN c.card_type is null THEN 'regular'
        ELSE c.card_type
    END AS card_type
FROM FANFIX_RAW.FANFIX_PAYMENT.PUBLIC_PAYMENTS p
LEFT JOIN FANFIX_RAW.FANFIX_PAYMENT.PUBLIC_CARDS c ON c.id = p.card_id
-- WHERE tip_type != '<nil>'
-- WHERE sales_tax_amount_in_cents> 0 AND REPLACE(try_parse_json(SALES_TAX_BILLING_LOCATION):region, '"','') is NOT null AND created_at > '2023-08-30'
ORDER BY 1 DESC