{{ config(materialized='table') }}

WITH first_message AS (
    SELECT
        date_trunc('day', m.created_at) AS date
        , m.sender_user_id
        , m.recipient_user_id
        , MIN(date_trunc('day', m.created_at)) over (partition by m.SENDER_USER_ID, m.RECIPIENT_USER_ID) AS first_message_day
    FROM FANFIX_RAW.FANFIX.USERS u
    LEFT JOIN FANFIX_RAW.fanfix_messaging.MESSAGES m
    ON u.ID = m.sender_user_id
    WHERE u.roles like '%fan%'
)
, first_message_count AS (
    SELECT
        sender_user_id AS fan_fanfix_id
        , date
        , COUNT(DISTINCT recipient_user_id) AS message_count
    FROM FIRST_MESSAGE
    WHERE date = first_message_day
    GROUP BY 1,2
)
, cumulative_message AS (
    SELECT
        FAN_FANFIX_ID
        , date
        , message_count
        , SUM(message_count) over (partition by fan_fanfix_id ORDER BY date ASC) AS cumulative_message_count
    FROM FIRST_MESSAGE_COUNT
)
SELECT
    FAN_FANFIX_ID
    , MAX(cumulative_message_count) AS lifetime_creators_messaged_count
    , CASE
        WHEN MAX(cumulative_message_count) > 3 THEN True
        ELSE False
    END AS creators_messaged_milestone_achieved
    , MIN(
        CASE
            WHEN cumulative_message_count > 3 THEN date
            ELSE null
        END
    )AS creators_messaged_milestone_achieved_date
FROM CUMULATIVE_MESSAGE
GROUP BY 1
ORDER BY 2 desc