version: 2

sources:
  - name: fanfix_user
    database: "{{var('fanfix_database', 'fanfix_raw')}}"
    schema: "{{var('fanfix_schema', 'fanfix_user')}}" # default value = 'fanfix_user'
    description: "User authentication and account management database from backend services"

    tables:
      - name: users
        description: Core user account records including authentication, email, and basic account information

      - name: agencies
        description: Agency accounts that manage multiple creators and handle business relationships

      - name: accounts
        description: Account-level settings and configurations for users and organizations
