version: 2

sources:
  - name: stripe
    description: Stripe payment processing data including charges, customers, invoices, and account information
    database: "{{var('stripe_database')}}"
    schema: stripe
    tables:
      - name: charges
        description: Stripe charge records representing payment attempts and their outcomes

      - name: connected_accounts
        description: Stripe Connect accounts linked to the platform for marketplace functionality
      - name: connected_accounts_metadata
        description: Additional metadata and custom fields for Stripe Connect accounts
      - name: customers
        description: Stripe customer records containing billing and contact information
      - name: customers_metadata
        description: Additional metadata and custom fields for Stripe customers
      - name: application_fees
        description: Fees collected by the platform from connected accounts in marketplace transactions
      - name: disputes
        description: Chargeback and dispute records for contested payments
      - name: payment_intents_metadata
        description: Additional metadata for Stripe Payment Intents including custom fields
      - name: transfers
        description: Money transfers between Stripe accounts and bank accounts
      - name: refunds
        description: Refund transactions for previously successful charges
      - name: invoices
        description: Stripe invoices for subscription billing and one-time payments
      - name: invoice_line_items
        description: Individual line items and charges within Stripe invoices
      