version: 2

sources:
  - name: firebase
    description: This is a legacy fanfix DB that was retired in 2023. We are maintaining it for the historical data
    schema: firebase
    database: fanfix_raw
    tables:
      - name: firebase_prod_user
      - name: firebase_prod_username
      - name: firebase_prod_users

  - name: fanfix_user 
    description: This is a replica of the User Postgres database built and maintained by Fanfix Product Eng Team
    schema: fanfix_user
    database: fanfix_raw
    tables:
      - name: users
      - name: accounts
      - name: agencies

  - name: fanfix
    description: This is a replica of the Fanfix Postgres database built and maintained by Fanfix Product Eng Team
    schema: fanfix
    database: fanfix_raw
    tables:
      - name: user_profiles
      - name: creator_profiles
      - name: fan_lists
      - name: posts
      - name: post_assets

  - name: fanfix_payment
    schema: fanfix_payment
    description: This is a replica of the Payment Postgres database built and maintained by Fanfix Product Eng Team
    database: fanfix_raw
    tables:
      - name: public_payments
      - name: public_cards
  
  - name: fanfix_subscription
    schema: fanfix_subscription
    description: This is a replica of the Subscription Postgres database built and maintained by Fanfix Product Eng Team
    database: fanfix_raw
    tables:
      - name: invoices
      - name: invoice_payments
      - name: subscriptions
      - name: subscription_plans
      - name: subscription_plan_perks
      - name: subscriptions_prices


  - name: hubspot
    description: This is hubspot data that we imported from the vendor
    schema: hubspot
    database: fanfix_raw
    tables:
      - name: contacts
      - name: companies
      - name: owners

  - name: posthog
    schema: posthog
    description: This is web tracking data that we imported from the vendor posthog
    database: fanfix_raw
    tables:
      - name: "persons"