with source as (
    select * from {{ source('fanfix_subscription', 'subscriptions') }}
),
renamed as (
    select 
        id as subscription_id,
        user_id as subscriber_user_id,
        creator_id,
        subscription_plan_id,
        status,
        current_period_start,
        current_period_end,
        trial_start,
        trial_end,
        canceled_at,
        ended_at,
        created_at,
        updated_at,
        stripe_subscription_id,
        is_trial,
        auto_renew
    from source
)
select * from renamed
