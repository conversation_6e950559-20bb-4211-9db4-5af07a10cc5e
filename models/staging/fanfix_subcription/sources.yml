version: 2

sources:
  - name: fanfix_subscription
    schema: fanfix_subscription
    description: Subscription management database containing subscription plans, billing, and recurring payment data
    database: fanfix_raw
    tables:
      - name: invoices
        description: Subscription billing invoices including amounts, due dates, and payment status

      - name: invoice_payments
        description: Payment records linked to subscription invoices and billing cycles

      - name: subscriptions
        description: Active and historical subscription records linking users to creator subscription plans

      - name: subscription_plans
        description: Creator-defined subscription tiers with pricing and access level configurations

      - name: subscription_plan_perks
        description: Benefits and perks associated with different subscription plan tiers

      - name: subscriptions_prices
        description: Pricing information and historical price changes for subscription plans
