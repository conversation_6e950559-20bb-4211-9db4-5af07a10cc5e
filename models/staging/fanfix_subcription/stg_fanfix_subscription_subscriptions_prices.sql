with source as (
    select * from {{ source('fanfix_subscription', 'subscriptions_prices') }}
),
renamed as (
    select 
        id as price_id,
        subscription_plan_id,
        price,
        currency,
        billing_interval,
        effective_date,
        end_date,
        is_active,
        created_at,
        updated_at,
        stripe_price_id,
        price_change_reason
    from source
)
select * from renamed
