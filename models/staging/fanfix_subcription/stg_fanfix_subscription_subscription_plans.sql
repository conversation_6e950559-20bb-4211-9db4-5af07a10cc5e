with source as (
    select * from {{ source('fanfix_subscription', 'subscription_plans') }}
),
renamed as (
    select 
        id as subscription_plan_id,
        creator_id,
        plan_name,
        plan_description,
        price,
        currency,
        billing_interval,
        trial_period_days,
        is_active,
        created_at,
        updated_at,
        stripe_price_id,
        stripe_product_id,
        access_level,
        max_subscribers
    from source
)
select * from renamed
