with source as (
    select * from {{ source('fanfix_payment', 'public_cards') }}
),
renamed as (
    select 
        id as card_id,
        user_id,
        stripe_customer_id,
        stripe_payment_method_id,
        card_brand,
        last_four_digits,
        exp_month,
        exp_year,
        is_default,
        is_active,
        billing_address_line1,
        billing_address_line2,
        billing_city,
        billing_state,
        billing_country,
        billing_zip,
        created_at,
        updated_at,
        deleted_at
    from source
)
select * from renamed
